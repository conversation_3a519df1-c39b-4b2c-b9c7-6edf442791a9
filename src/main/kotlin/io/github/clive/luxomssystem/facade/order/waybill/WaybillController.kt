package io.github.clive.luxomssystem.facade.order.waybill

import io.github.clive.luxomssystem.application.waybill.WaybillApplicationService
import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.facade.order.waybill.request.UpdateWaybillRequest
import io.github.clive.luxomssystem.facade.order.waybill.request.WaybillPageRequest
import io.github.clive.luxomssystem.facade.order.waybill.response.WaybillResponse
import org.springframework.http.ContentDisposition
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("/api/waybills")
class WaybillController(
    private val waybillApplicationService: WaybillApplicationService,
) {
    @PostMapping("/push/{id}")
    fun pushWaybill(
        @PathVariable id: Long,
    ) {
        waybillApplicationService.pushWaybill(id)
    }

    @PutMapping("/{id}")
    fun updateWaybill(
        @PathVariable id: Long,
        @RequestBody request: UpdateWaybillRequest,
    ): WaybillResponse = waybillApplicationService.updateWaybill(id, request)

    @PostMapping("/supplier-scanned-count")
    fun getSupplierScannedCount(
        @RequestBody ids: List<Long>,
    ) = waybillApplicationService.countSupplierOrderScanned(ids)

    @PostMapping("/{id}/retry")
    fun retry(
        @PathVariable id: Long,
    ) {
        waybillApplicationService.retry(id)
    }

    @PostMapping("/retry/batch")
    fun retryBatch(
        @RequestBody ids: List<Long>,
    ) {
        waybillApplicationService.retryBatch(ids)
    }

    @GetMapping("/{id}")
    fun getWaybill(
        @PathVariable id: Long,
    ): WaybillResponse = waybillApplicationService.getWaybillById(id)

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun deleteWaybill(
        @PathVariable id: Long,
    ) {
        waybillApplicationService.deleteWaybill(id)
    }

    @PatchMapping("/{id}/status")
    fun updateStatus(
        @PathVariable id: Long,
        @RequestParam newStatus: String,
    ): WaybillResponse {
        waybillApplicationService.updateStatus(id, newStatus)
        return waybillApplicationService.getWaybillById(id)
    }

    @GetMapping("/page")
    fun page(req: WaybillPageRequest) = waybillApplicationService.pageQuery(req)

    @GetMapping("/failed/page")
    fun failedPage(req: WaybillPageRequest) = waybillApplicationService.failedPage(req)

    @PostMapping("/{id}/retryPush")
    fun retryPush(
        @PathVariable id: Long,
    ) {
        waybillApplicationService.retryPush(id)
    }

    @GetMapping("/day/status/count")
    fun countByStatus(select: Int) = waybillApplicationService.getWayBillStatusCounts(select)

    @DeleteMapping("/batch")
    fun deleteWaybillBatch(
        @RequestBody ids: List<Long>,
    ) {
        waybillApplicationService.deleteWaybillBatch(ids)
    }

    @PostMapping("/{id}/:cancel")
    fun cancelWaybill(
        @PathVariable id: Long,
        @RequestParam reason: String = "",
    ) {
        waybillApplicationService.cancelWaybill(id, reason)
    }

    @GetMapping("/statistic/finance")
    fun statisticFinance(
        @RequestParam mainOrderId: Long,
    ): FinanceStatisticResponse = waybillApplicationService.statisticFinance(mainOrderId)

    @GetMapping("/export/finance")
    fun exportFinance(
        @RequestParam mainOrderId: Long,
    ): ResponseEntity<ByteArray> {
        val data = waybillApplicationService.exportFinance(mainOrderId)
        val contentDisposition = ContentDisposition.attachment().filename("finance_$mainOrderId").build()
        return ResponseEntity
            .status(HttpStatus.OK)
            .header(HttpHeaders.CONTENT_DISPOSITION, contentDisposition.toString())
            .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
            .body(data)
    }

    @PostMapping("/quick-channel/tax:set")
    fun setQuickChannelTax(
        @RequestBody request: SetQuickChannelTaxRequest,
    ) {
        waybillApplicationService.setQuickChannelTax(request)
    }

    @PostMapping("/channel:change")
    fun changeChannel(
        @RequestBody request: BatchChangeWayBillChannelRequest,
    ) {
        waybillApplicationService.changeChannel(request)
    }

    @PostMapping("/stop/print")
    fun stopPrint(
        @RequestBody req: StopWaybillPrintRequest,
    ) {
        waybillApplicationService.stopPrint(req)
    }

    @PostMapping("/stop/print/cancel")
    fun stopPrintCancel(
        @RequestBody req: CancelWaybillStopPrintRequest,
    ) {
        waybillApplicationService.stopPrintCancel(req)
    }

    @PostMapping("/export/excel")
    fun exportExcel(
        @RequestBody req: ExportExcelRequest,
    ): ResponseEntity<ByteArray> {
        val excelBytes = waybillApplicationService.exportExcel(req.ids)

        val formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
        val fileName = "${req.fileName}_${LocalDateTime.now().format(formatter)}.xlsx"

        return ResponseEntity
            .ok()
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .headers { headers ->
                headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=$fileName")
                headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition")
            }.body(excelBytes)
    }

    @PostMapping("/extend")
    fun extend(
        @RequestBody request: ExtendWaybillRequest,
    ): String = waybillApplicationService.extend(request)

    @PostMapping("/{id}/submit")
    fun submitWaybill(
        @PathVariable id: Long,
        @RequestBody request: SubmitWaybillRequest,
    ) {
        waybillApplicationService.submitWaybill(id, request.waybillNo)
    }
}

data class ExtendWaybillRequest(
    val id: Long,
    val newOrderNo: String?,
)

data class ExportExcelRequest(
    val ids: List<Long>,
    val fileName: String,
)

data class StopWaybillPrintRequest(
    val ids: List<Long>,
    val warning: String,
)

data class CancelWaybillStopPrintRequest(
    val ids: List<Long>,
)

data class BatchChangeWayBillChannelRequest(
    val ids: List<Long>,
    val channel: WaybillChannel,
    val method: String,
)

data class SetQuickChannelTaxRequest(
    val waybillIds: List<Long>,
    val tax: BigDecimal,
)

data class FinanceStatisticResponse(
    val totalCustomerNeedPayCost: BigDecimal = BigDecimal.ZERO,
    val totalCustomerNeedPayTax: BigDecimal = BigDecimal.ZERO,
    val totalQuickChannelTax: BigDecimal = BigDecimal.ZERO,
    val totalWaybillCost: BigDecimal = BigDecimal.ZERO,
)

data class SubmitWaybillRequest(
    val waybillNo: String,
)
