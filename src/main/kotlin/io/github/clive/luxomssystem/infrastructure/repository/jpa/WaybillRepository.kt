@file:Suppress("JpaQlInspection")

package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.facade.order.waybill.FinanceStatisticResponse
import io.github.clive.luxomssystem.infrastructure.config.AuthNativeRewriter
import io.github.clive.luxomssystem.infrastructure.config.AuthRewriter
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface WaybillRepository : JpaRepository<Waybill, Long> {
    fun findByOrderNo(orderNo: String): Waybill?

    @Query(
        """
        SELECT w.* FROM waybills w
        WHERE string_to_array(w.order_nos, ',') @> ARRAY[cast(:orderNo AS text)]::text[]
        """,
        nativeQuery = true,
    )
    fun findByWaybillAccordingToOrderNo(orderNo: String): List<Waybill>

    @Query(
        """
        SELECT w.*
        FROM waybills w
        WHERE 1=1
            AND w.biz_id = :bizId
            AND (:orderNo IS NULL OR w.order_nos ILIKE CONCAT('%', :orderNo, '%'))
            AND (:#{#status.isEmpty()} = true OR w.status IN (:status))
            AND (:spu IS NULL OR w.spu ILIKE CONCAT('%', :spu, '%'))
            AND (:mainOrderId IS NULL OR w.main_order_id = :mainOrderId)
            AND (:createdAtFrom IS NULL OR w.created_at >= :createdAtFrom)
            AND (:createdAtTo IS NULL OR w.created_at <= :createdAtTo)
            AND (:fileName IS NULL OR w.file_name ILIKE CONCAT('%', :fileName, '%'))
            AND (:waybillNo IS NULL OR w.waybill_no ILIKE CONCAT('%', :waybillNo, '%'))
            AND (:orderNos IS NULL OR string_to_array(w.order_nos, ',') && string_to_array(:orderNos, ','))
            and (:#{#waybillNos.isEmpty()} = true or w.waybill_no IN :waybillNos)
            AND (:country IS NULL OR UPPER(w.country) = UPPER(:country) OR UPPER(w.country) = UPPER(:countryCode))

        ORDER BY w.created_at DESC
    """,
        nativeQuery = true,
        queryRewriter = AuthNativeRewriter::class,
    )
    fun findByBizId(
        bizId: Long,
        orderNo: String?,
        status: List<String>,
        pageable: Pageable,
        spu: String?,
        mainOrderId: Long?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        fileName: String?,
        waybillNo: String?,
        orderNos: String?,
        waybillNos: List<String>,
        country: String?,
        countryCode: String?,
    ): Page<Waybill>

    @Query(
        """
        SELECT so.status as status, COUNT(so) as count
        FROM waybills so
        WHERE so.created_at >= :startDate and biz_id = :bizId
        GROUP BY so.status
    """,
        nativeQuery = true,
    )
    fun countWaybillByStatusAndTimeRange(
        @Param("startDate") startDate: Long,
        @Param("bizId") bizId: Long,
    ): List<Array<Any>>

    @Query(
        """
        select count(w) from waybills w where
        1=1 and 
        (:status is null or w.status = :status)
        and w.createdAt >= :createdAtFrom
        and w.createdAt <= :createdAtTo
        and w.bizId = :bizId
    """,
        queryRewriter = AuthRewriter::class,
    )
    fun countByStatusAndCreatedAtBetween(
        status: WayBillStatus?,
        createdAtFrom: Long?,
        createdAtTo: Long?,
        bizId: Long,
    ): Int

    @Query(
        """
        SELECT new io.github.clive.luxomssystem.facade.order.waybill.FinanceStatisticResponse(
            COALESCE(SUM(w.customerNeedPayCost), 0),
            COALESCE(SUM(w.customerNeedPayTax), 0),
            COALESCE(SUM(w.quickChannelTax), 0),
            COALESCE(SUM(w.customerNeedPayCost + w.customerNeedPayTax + w.quickChannelTax), 0)
        )
        FROM waybills w 
        WHERE w.mainOrderId = :mainOrderId and w.bizId = :bizId
        GROUP BY w.mainOrderId
    """,
    )
    fun getFinanceStatistic(
        @Param("mainOrderId") mainOrderId: Long,
        bizId: Long,
    ): FinanceStatisticResponse?

    @Query(
        """
        SELECT new io.github.clive.luxomssystem.facade.order.waybill.FinanceStatisticResponse(
            COALESCE(SUM(w.customerNeedPayCost), 0),
            COALESCE(SUM(w.customerNeedPayTax), 0),
            COALESCE(SUM(w.quickChannelTax), 0),
            COALESCE(SUM(w.customerNeedPayCost + w.customerNeedPayTax + w.quickChannelTax), 0)
        )
        FROM waybills w 
        WHERE w.customerId = :customerId and w.bizId = :bizId
    """,
    )
    fun getFinanceStatisticByCustomer(
        @Param("customerId") customerId: Long,
        bizId: Long,
    ): FinanceStatisticResponse?

    fun findByMainOrderId(mainOrderId: Long): List<Waybill>

    fun findByMainOrderIdAndStatus(
        mainOrderId: Long,
        status: WayBillStatus,
    ): List<Waybill>

    /**
     * 查找指定状态的运单
     */
    fun findByStatus(status: WayBillStatus): List<Waybill>

    /**
     * 查找OUTBOUND状态且运单号不为空的运单
     */
    @Query(
        """
        SELECT w FROM waybills w
        WHERE w.status = 'OUTBOUND'
        AND w.waybillNo IS NOT NULL
        AND w.waybillNo != ''
        ORDER BY w.updatedAt ASC
    """,
    )
    fun findOutboundWaybillsWithTrackingNumber(): List<Waybill>

    /**
     * 查找OUTBOUND状态且指定渠道的运单
     */
    @Query(
        """
        SELECT w FROM waybills w
        WHERE w.status = 'OUTBOUND'
        AND w.waybillNo IS NOT NULL
        AND w.waybillNo != ''
        AND w.shipping.channel = :channel
        ORDER BY w.updatedAt ASC
    """,
    )
    fun findOutboundWaybillsByChannel(
        @Param("channel") channel: io.github.clive.luxomssystem.common.enums.WaybillChannel,
    ): List<Waybill>

    data class WayBillStatusCount(
        val status: WayBillStatus,
        val count: Long,
    )

    fun deleteByMainOrderId(mainOrderId: Long)
}

fun WaybillRepository.getWaybillStatusCounts(
    startDate: Long,
    bizId: Long,
): List<WaybillRepository.WayBillStatusCount> {
    val results = countWaybillByStatusAndTimeRange(startDate, bizId)
    return results.map { row ->
        val status = WayBillStatus.valueOf(row[0] as String)
        val count = (row[1] as Number).toLong()
        WaybillRepository.WayBillStatusCount(status, count)
    }
}
